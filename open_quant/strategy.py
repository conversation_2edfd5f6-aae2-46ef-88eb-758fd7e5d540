import sys, types
from pathlib import Path
from datetime import datetime, timedelta, timezone

import traderv2  # type: ignore
import base_strategy
import json
import time


def get_millis(offset_hours=0, offset_minutes=0):
    now = datetime.now()
    delta = timedelta(hours=offset_hours, minutes=offset_minutes)
    target_time = now - delta
    return int(target_time.timestamp() * 1000)


def get_today_start_millis():
    now = datetime.now(timezone.utc)
    utc_midnight = datetime(year=now.year, month=now.month, day=now.day, tzinfo=timezone.utc)
    millis = int(utc_midnight.timestamp() * 1000)
    return millis


class Strategy(base_strategy.BaseStrategy):
    def __init__(self, cex_configs, dex_configs, config, trader: traderv2.TraderV2):
        self.trader = trader
        self.swap_balance = {}
        self.spot_balance = {}
        self.swap_total_balance = 0
        self.spot_total_balance = 0
        self.total_balance = 0
        self.trade_count = 0
        self.trade_volume = 0.0
        self.assets = [
            "SOL",
            "XRP",
            "BNB",
            "ETH",
            "SUI",
            "BCH",
            "APT",
            "ETC",
            "NEAR",
            "INJ",
            "ADA",
            "LTC",
            "USDT",
            "USDC",
            "DOT",
            "XLM",
            "HBAR",
            "POL",
            "UNI",
            "SEI",
            "BTC",
        ]
        self.symbols = [f"{asset}_USDT" for asset in self.assets]
        self.asset_close_price = {}
        self.web_client_initialized = False
        self.swap_positions = {}
        self.last_trade_getting_time = max(get_millis(offset_hours=23, offset_minutes=58), get_today_start_millis())
        self.total_trades_volume = 0
        self.total_trades_count = 0
        self.trades_by_symbol = {}
        self.total_fees = 0
        self.trading_pairs = set()
        self.last_update_balance_time = datetime.now()
        with open("bn_margin_circles.json", "r") as f:
            trading_pairs_json = json.load(f)
            for circle in trading_pairs_json:
                for p in circle:
                    self.trading_pairs.add(p[0].replace("/", ""))

    def name(self):
        return "三角套利_bn_1"

    def start(self):
        self._init_account_data()
        self._get_trades_volume()
        self.start_time = datetime.now()

    def subscribes(self):
        subs = [
            {
                "account_id": 0,
                "sub": {
                    "SubscribeWs": [{"Kline": {"symbols": self.symbols, "interval": "1m"}}],
                },
            },
            {
                "account_id": 0,
                "sub": {
                    "SubscribeRest": {"update_interval": {"secs": 10, "nanos": 0}, "rest_type": "Balance"},
                },
            },
            {
                "account_id": 1,
                "sub": {"SubscribeRest": {"update_interval": {"secs": 10, "nanos": 0}, "rest_type": "Balance"}},
            },
            {
                "account_id": 0,
                "sub": {"SubscribeRest": {"update_interval": {"secs": 10, "nanos": 0}, "rest_type": "Position"}},
            },
        ]
        return subs

    def on_kline(self, exchange, kline):
        symbol = kline["symbol"]
        asset = symbol.split("_")[0]
        self.asset_close_price[asset] = kline["candles"][-1]["close"]

    def on_balance(self, account_id, balances):
        if account_id == 0:
            self.swap_balance = {}
            for b in balances:
                if b["balance"] != 0:
                    print(f"balance: {b['balance']}, unrealized_pnl: {b['unrealized_pnl']}")
                    self.swap_balance[b["asset"]] = b["balance"]
        elif account_id == 1:
            self.spot_balance = {}
            for b in balances:
                if b["balance"] != 0:
                    self.spot_balance[b["asset"]] = b["balance"]
        else:
            print(f"unknown account_id: {account_id}")
        self._update_balance()

    def on_position(self, account_id, positions):
        total_unrealized_pnl = 0
        for p in positions:
            symbol = p["symbol"]
            asset = symbol.split("_")[0]
            if p["amount"] > 0:
                self.swap_positions[symbol] = p
                total_unrealized_pnl += p["unrealized_pnl"]
        print(f"position total_unrealized_pnl: {total_unrealized_pnl}")

    def _init_account_data(self):
        print("开始初始化账户数据...")
        cmds = []
        cmds.append({"account_id": 0, "method": "Balance", "sync": True})
        cmds.append({"account_id": 1, "method": "Balance", "sync": True})
        try:
            results = self.trader.batch_publish(cmds)
            if len(results) > 0 and results[0].get("Ok"):
                balance_info = results[0]["Ok"]
                for b in balance_info:
                    if b["balance"] != 0:
                        self.swap_balance[b["asset"]] = b["balance"]
                    elif b["asset"] in self.swap_balance:
                        del self.swap_balance[b["asset"]]
                print(f"swap balance: {self.swap_balance}")
            if len(results) > 1 and results[1].get("Ok"):
                balance_info = results[1]["Ok"]
                for b in balance_info:
                    if b["balance"] != 0:
                        self.spot_balance[b["asset"]] = b["balance"]
                    elif b["asset"] in self.spot_balance:
                        del self.spot_balance[b["asset"]]
                print(f"spot balance: {self.spot_balance}")
        except Exception as e:
            print(f"初始化账户数据时出错: {e}")

    def _init_web_client(self):
        now = datetime.now()
        if now - self.start_time < timedelta(seconds=30):
            return
        total_balance = 0
        for b in self.swap_balance:
            total_balance += self.swap_balance[b]
        for b in self.spot_balance:
            total_balance += self.spot_balance[b]
        print(f"initial total balance: {total_balance}")
        web_config = {
            "server_name": "三角套利_bn_1",
            "primary_balance": total_balance,
            "is_production": True,
        }
        self.trader.init_web_client(web_config)
        self.trader.start_web_client(upload_interval=60)
        print(f"Web客户端配置完成: {web_config}")
        self.web_client_initialized = True

    def _update_balance(self):
        if datetime.now() - self.last_update_balance_time < timedelta(seconds=120):
            return
        if not self.web_client_initialized:
            self._init_web_client()
            return
        self.swap_total_balance = 0
        self.spot_total_balance = 0
        self.total_balance = 0
        for b in self.swap_balance:
            self.swap_total_balance += self.swap_balance[b]
        print(f"swap balance: {self.swap_total_balance}")
        for b in self.spot_balance:
            if b not in self.asset_close_price and b != "USDT":
                print(f"资产{b}的价格未更新")
                return
            asset_price = self.asset_close_price[b] if b != "USDT" else 1
            self.spot_total_balance += self.spot_balance[b] * asset_price
        self.total_balance = self.swap_total_balance + self.spot_total_balance
        self.trader.update_total_balance(
            primary_balance=self.total_balance,
        )
        print(f"update balance: {self.total_balance}")
        self._upload_table()
        self.last_update_balance_time = datetime.now()

    def _upload_table(self):
        self._get_trades_volume()
        pnl_data = self._get_pnl()
        self.trader.log_profit(pnl_data["pnl"])
        pnl_rows = []
        win_rate = (
            pnl_data["wins"] / (pnl_data["wins"] + pnl_data["losses"])
            if pnl_data["wins"] + pnl_data["losses"] > 0
            else 0
        )
        now = datetime.now(timezone.utc)
        midnight_utc = datetime(year=now.year, month=now.month, day=now.day, tzinfo=timezone.utc)
        delta = now - midnight_utc
        hours_float = delta.total_seconds() / 3600
        annual_return = pnl_data["pnl"] / hours_float * 24 * 365 / self.total_balance
        daily_return = pnl_data["pnl"] / hours_float * 24 / self.total_balance
        pnl = pnl_data["pnl"]
        pnl_rows.append(
            [
                pnl_data["wins"],
                pnl_data["losses"],
                f"{win_rate:.1%}",
                f"{pnl:.3f}",
                f"{daily_return:.2%}",
                f"{annual_return:.2%}",
                f"{self.spot_total_balance:.2f}",
                f"{self.swap_total_balance:.2f}",
                f"{self.total_balance:.2f}",
                f"{self.total_trades_volume:.1f}",
                self.total_trades_count,
                f"{self.total_fees:.6f}",
            ]
        )
        pnl_table = {
            "title": "盈亏统计",
            "cols": [
                "胜",
                "负",
                "胜率",
                "盈亏",
                "日化",
                "年化",
                "现货总资产",
                "合约总资产",
                "总资产",
                "当日交易量(USDT)",
                "当日交易次数",
                "当日手续费(BNB)",
            ],
            "rows": pnl_rows,
        }

        position_rows = []
        for b in self.spot_balance:
            if b not in self.asset_close_price and b != "USDT":
                print(f"资产{b}的价格未更新")
                return
            asset_price = self.asset_close_price[b] if b != "USDT" else 1
            position_rows.append(
                [
                    b,
                    self.spot_balance[b],
                    asset_price * self.spot_balance[b],
                ]
            )

        position_table = {
            "title": "现货持仓",
            "cols": ["交易对", "当前余额", "持仓价值(USDT)"],
            "rows": position_rows,
        }

        swap_rows = []
        for symbol, pos in self.swap_positions.items():
            asset = symbol.split("_")[0]
            value = pos["amount"] * self.asset_close_price[asset]
            swap_rows.append([asset, pos["amount"], value, pos["unrealized_pnl"]])

        swap_table = {
            "title": "合约持仓",
            "cols": ["交易对", "当前持仓", "持仓价值(USDT)", "浮动盈亏"],
            "rows": swap_rows,
        }
        self.trader.upload_tables(
            [
                pnl_table,
                position_table,
                swap_table,
            ]
        )

    def _get_pnl(self):
        """
        {
          "utc_date": "2025-08-18",
          "wins": 30,
          "losses": 19,
          "pnl": 0.18289987485202644,
          "updated_at": "2025-08-18T03:31:38.413955Z",
          "source_file": "20250817_022908.log",
          "source_inode": 295939
        }%
        """
        with open("win_rate.json", "r") as f:
            data = json.load(f)
        return data

    def _get_trades_volume(self):
        now = get_millis()
        if now - self.last_trade_getting_time < 120000:
            return
        for symbol in self.trading_pairs:
            now_millis = get_millis()
            start_millis = self.last_trade_getting_time
            if now_millis - start_millis > 86400000:
                start_millis = now_millis - ********
            trades_volume = 0
            trades_count = 0
            fees = 0
            while True:
                time.sleep(0.5)
                params = {
                    "symbol": symbol,
                    "startTime": start_millis,
                    "endTime": now_millis,
                    "limit": 1000,
                    "recvWindow": 60000,
                }
                result = self.trader.request(
                    account_id=1,
                    method="GET",
                    path="/api/v3/myTrades",
                    query=params,
                    auth=True,
                )
                if "Ok" in result:
                    trades = result["Ok"]
                    if len(trades) == 0:
                        break
                    fees += sum(float(t["commission"]) for t in trades)
                    trades_count += len(trades)
                    trades_volume = sum(float(t["quoteQty"]) for t in trades)
                    found = False
                    if symbol.endswith("USDT"):
                        quote_price = 1
                        trades_volume = trades_volume
                        found = True
                    else:
                        for s in self.assets:
                            if symbol.endswith(s):
                                if s not in self.asset_close_price:
                                    print(f"asset {s}USDT price not found")
                                    return
                                quote_price = self.asset_close_price[s]
                                trades_volume = trades_volume * quote_price
                                found = True
                                break
                    if not found:
                        trades_volume += trades_volume
                    last_trade_time = trades[-1]["time"]
                else:
                    print(f"get_trades_volume error: {result}")
                    break
                start_millis = last_trade_time + 1
                now_millis = get_millis()
                if now_millis - start_millis < 60000:
                    break
            if symbol not in self.trades_by_symbol:
                self.trades_by_symbol[symbol] = {
                    "total_trades_volume": trades_volume,
                    "total_trades_count": trades_count,
                    "total_fees": fees,
                }
            else:
                self.trades_by_symbol[symbol]["total_trades_volume"] += trades_volume
                self.trades_by_symbol[symbol]["total_trades_count"] += trades_count
                self.trades_by_symbol[symbol]["total_fees"] += fees
            self.total_trades_volume += trades_volume
            self.total_trades_count += trades_count
            self.total_fees += fees
        print(f"total_trades_volume: {self.total_trades_volume}")
        print(f"total_trades_count: {self.total_trades_count}")
        print(f"total_fees in BNB: {self.total_fees}")
        if self.last_trade_getting_time < get_today_start_millis():
            self.last_trade_getting_time = get_today_start_millis()
            self.total_trades_volume = 0
            self.total_trades_count = 0
            self.total_fees = 0
        else:
            self.last_trade_getting_time = get_millis()
