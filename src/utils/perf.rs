use crate::logln;

pub fn percentile(data: &mut [f64], percentile: f64) -> f64 {
    if data.is_empty() {
        return 0.0;
    }

    data.sort_by(|a, b| a.partial_cmp(b).unwrap());
    let len = data.len();
    let rank = percentile * (len - 1) as f64;
    let lower = rank.floor() as usize;
    let upper = rank.ceil() as usize;

    if lower == upper {
        data[lower]
    } else {
        let weight = rank - lower as f64;
        let lower_val = data[lower] as f64;
        let upper_val = data[upper] as f64;
        (lower_val + (upper_val - lower_val) * weight).round() as f64
    }
}

#[cfg(target_arch = "x86_64")]
pub fn now() -> u64 {
    unsafe { std::arch::x86_64::_rdtsc() }
}

pub fn system_now_in_us() -> u64 {
    std::time::SystemTime::now()
        .duration_since(std::time::UNIX_EPOCH)
        .unwrap()
        .as_micros() as u64
}

pub fn system_now_in_ms() -> u64 {
    std::time::SystemTime::now()
        .duration_since(std::time::UNIX_EPOCH)
        .unwrap()
        .as_millis() as u64
}

#[cfg(target_arch = "aarch64")]
pub fn now() -> u64 {
    unsafe {
        let val: u64;
        core::arch::asm!(
            "mrs {val}, cntvct_el0",
            val = out(reg) val,
            options(nomem, nostack, preserves_flags)
        );
        val
    }
}

#[cfg(target_arch = "x86_64")]
pub fn cpu_freq() -> u64 {
    2_000_000_000
}

#[cfg(target_arch = "aarch64")]
pub fn cpu_freq() -> u64 {
    // cntfrq_el0
    unsafe {
        let val: u64;
        core::arch::asm!(
            "mrs {val}, cntfrq_el0",
            val = out(reg) val,
            options(nomem, nostack, preserves_flags)
        );
        val
    }
}

pub fn circles_to_ns(cycles: u64) -> f64 {
    let freq = cpu_freq();
    (cycles as f64 * 1_000_000_000.0) / (freq as f64)
}

pub struct MeasureGuard {
    start: u64,
    name: &'static str,
}

impl MeasureGuard {
    pub fn new(name: &'static str) -> Self {
        Self { start: now(), name }
    }
}

impl Drop for MeasureGuard {
    fn drop(&mut self) {
        let elapsed = now() - self.start;
        let mut ns_buffer = ryu::Buffer::new();
        let ns_str = ns_buffer.format(circles_to_ns(elapsed));
        logln!("{}: {} ns", self.name, ns_str);
    }
}

#[cfg(test)]
mod tests {
    use perf_macro::measure;

    use super::*;

    #[measure]
    fn measure_guard_tester() {
        let _guard = MeasureGuard::new("test_measure_guard");
    }

    #[test]
    fn test_measure_guard() {
        measure_guard_tester();
    }

    #[test]
    fn test_circle_to_ns() {
        let start = now();
        std::thread::sleep(std::time::Duration::from_millis(5));
        let end1 = now();
        std::thread::sleep(std::time::Duration::from_millis(10));
        let end2 = now();
        assert!(
            (circles_to_ns(end1 - start) - 5_000_000.0).abs() < 1000.0,
            "Expected ~0ms, got {}ns",
            circles_to_ns(end1 - start)
        );

        let ns = circles_to_ns(end2 - end1);
        // Allow some tolerance for timing inaccuracy (±2ms)
        assert!(
            (ns - 5_000_000.0).abs() < 2_000.0,
            "Expected ~0ms, got {}ns",
            ns
        );
    }
}
