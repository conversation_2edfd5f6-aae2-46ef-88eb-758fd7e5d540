pub const FUTURES_IN_LEN: usize = 1024 * 64;
pub const FUTURES_OUT_LEN: usize = 1024 * 4;
pub const FUTURES_SYMBOL: &str = "ETHUSDC";
pub const FUTURES_API_KEY: &str =
    "I6ZeHYptYtPBYGj1uQYx944Eb2Caoi20VnGIuKRKjih852Hg1bUjjy36UEyjaGLH";
pub const FUTURES_API_SECRET: &str = "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VwBCIEIB01rxfEYLKZlDFw/ieLChOmI7/fxN4eeH8bZPqJvR74\n-----END PRIVATE KEY-----";
pub const FUTURES_ORDER_PLACE_OFFSET: f64 = 3.0;
pub const FUTURES_TAKE_PROFIT_OFFSET: f64 = 1.0;
pub const FUTURES_MAKER_MIN_LEVEL: usize = 5;
pub const FUTURES_QUANTITY_TICK_SIZE: f64 = 0.005;
pub const FUTURES_PRICE_TICK_SIZE: f64 = 0.01;
