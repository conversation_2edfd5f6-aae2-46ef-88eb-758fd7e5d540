use crate::encoding::{book_ticker::FuturesBookTicker, gate::GateBookTicker};

pub struct ArbitrageGate {
    pub bn_bbo: Option<FuturesBookTicker>,
    pub gate_bbo: Option<GateBookTicker>,
}

impl ArbitrageGate {
    pub fn new() -> Self {
        Self {
            bn_bbo: None,
            gate_bbo: None,
        }
    }

    pub fn update_bn_bbo(&mut self, bbo: FuturesBookTicker) {
        self.bn_bbo = Some(bbo);
    }

    pub fn update_gate_bbo(&mut self, bbo: GateBookTicker) {
        self.gate_bbo = Some(bbo);
    }

    pub fn price_diff(&self) -> Option<f64> {
        if let (Some(bn_bbo), Some(gate_bbo)) = (&self.bn_bbo, &self.gate_bbo) {
            if (bn_bbo.bid_price - gate_bbo.bid_price).abs() > 0.00005 {
                Some(bn_bbo.bid_price - gate_bbo.bid_price)
            } else {
                None
            }
        } else {
            None
        }
    }
}
