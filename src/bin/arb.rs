use libwebsocket_rs::{
    engine::{arbitrage_runner::run, cancel_orders::cancel_orders, latency_measurement::measure},
    info,
};
use std::env;

fn print_usage() {
    info!("Usage: arb [OPTIONS]\n");
    info!("Options:\n");
    info!("  --measure, -m    Run latency measurement before starting arbitrage\n");
    info!("  --no-measure     Skip latency measurement and use default configuration\n");
    info!("  --help, -h       Show this help message\n");
    info!("\n");
    info!("If no options are provided, latency measurement will be run by default.\n");
}

fn main() -> std::result::Result<(), Box<dyn std::error::Error>> {
    let args: Vec<String> = env::args().collect();

    // 解析命令行参数
    let mut run_measure = true; // 默认运行测量

    for arg in args.iter().skip(1) {
        match arg.as_str() {
            "--measure" | "-m" => {
                run_measure = true;
            }
            "--no-measure" => {
                run_measure = false;
            }
            "--help" | "-h" => {
                print_usage();
                return Ok(());
            }
            _ => {
                libwebsocket_rs::error!("Unknown argument: {}", arg);
                print_usage();
                return Err("Invalid arguments".into());
            }
        }
    }

    libwebsocket_rs::utils::affinity::bind_to_core(0);

    cancel_orders();

    let (best_market_data_ip, best_trade_ip, best_depth_ip, best_order_ip) = if run_measure {
        info!("running latency measurement...");
        let result = measure();
        if result.0.is_none() || result.1.is_none() || result.2.is_none() {
            info!("cannot find best ip, use default config");
        }
        // avoid hit the order placing request limit, sleep 10s
        std::thread::sleep(std::time::Duration::from_secs(10));
        result
    } else {
        info!("skip latency measurement, use default config");
        (None, None, None, None)
    };

    run(
        best_market_data_ip,
        best_trade_ip,
        best_depth_ip,
        best_order_ip,
    )?;

    Ok(())
}
