use libwebsocket_rs::{
    CallbackData, Message, Result, Settings, WebSocket, WebSocketHandle,
    encoding::book_ticker::parse_futures_bookticker,
    engine::{
        binance::generate_futures_book_ticker_url,
        token::{WS_BBO_1, WS_BBO_2},
    },
    error, flush_logs, info,
    net::utils::url::Url,
    utils::{self, perf::system_now_in_ms},
};
use serde::{Deserialize, Serialize};
use serde_json::json;

const GATE_WS_URL: &str = "wss://fx-ws.gateio.ws/v4/ws/usdt";
const IN_LEN: usize = 1024 * 32;
const OUT_LEN: usize = 1024 * 4;
const GATE_SYMBOL: &str = "FLM_USDT";
const BN_SYMBOL: &str = "FLMUSDT";

fn generate_bbo_url() -> String {
    GATE_WS_URL.to_string()
}

fn generate_bbo_subscribe_request() -> String {
    let subscribe_msg = json!({
        "time": chrono::Utc::now().timestamp(),
        "channel": "futures.book_ticker",
        "event": "subscribe",
        "payload": [GATE_SYMBOL],
    });
    subscribe_msg.to_string()
}

fn main() -> std::result::Result<(), Box<dyn std::error::Error>> {
    let mut bn_last_mid_price = 0.0;
    let mut gate_last_mid_price = 0.0;
    let mut has_diff = false;
    let callback = move |handle: &mut WebSocketHandle<IN_LEN, OUT_LEN>,
                         cd: CallbackData|
          -> Result<()> {
        match cd {
            CallbackData::Message(token, msg) => match msg {
                Message::WebsocketPayload(data) => match token {
                    WS_BBO_1 => {
                        match serde_json::from_slice::<Envelope>(data.as_ref()) {
                            Ok(env) => {
                                // 错误响应
                                if let Some(err) = env.error {
                                    error!("Server error: {err}");
                                    return Ok(());
                                }

                                // 订阅确认
                                if env.event.as_deref() == Some("subscribe") {
                                    error!("Subscribed OK: {}", env.channel.unwrap_or_default());
                                    return Ok(());
                                }

                                // book_ticker 更新
                                if env.channel.as_deref() == Some("futures.book_ticker")
                                    && env.event.as_deref() == Some("update")
                                {
                                    if let Some(res) = env.result {
                                        // 解析成 BookTicker（字段类型存在不确定性，用 Value 兜底）
                                        if let Ok(bt) =
                                            serde_json::from_value::<BookTicker>(res.clone())
                                        {
                                            gate_last_mid_price = (bt.b.parse::<f64>().unwrap()
                                                + bt.a.parse::<f64>().unwrap())
                                                / 2.0;
                                            let price_diff =
                                                bn_last_mid_price - gate_last_mid_price;
                                            if price_diff.abs() > 0.00005 && !has_diff {
                                                error!(
                                                    "price diff: {:.6} bn mid price: {:.6} gate mid price: {:?}",
                                                    price_diff,
                                                    bn_last_mid_price,
                                                    gate_last_mid_price
                                                );
                                                has_diff = true;
                                            } else if has_diff && price_diff.abs() < 0.00002 {
                                                error!(
                                                    "price diff back to normal: {:.6} bn mid price: {:.6} gate mid price: {:?}",
                                                    price_diff,
                                                    bn_last_mid_price,
                                                    gate_last_mid_price
                                                );
                                                has_diff = false;
                                            }
                                        } else {
                                            // 字段类型变动时，直接打印原始 JSON 方便调试
                                            error!("book_ticker raw: {}", res);
                                        }
                                    }
                                }
                            }
                            Err(_) => {
                                // 有时服务端会写入非标准包或心跳信息
                                error!("Raw text: {:?}", String::from_utf8_lossy(data.as_ref()));
                            }
                        }
                    }
                    WS_BBO_2 => {
                        if let Some(bt) = parse_futures_bookticker(data.as_ref()) {
                            bn_last_mid_price = (bt.bid_price + bt.ask_price) / 2.0;
                            // error!("price diff: {:.6}", bn_last_mid_price - gate_last_mid_price);
                            let price_diff = bn_last_mid_price - gate_last_mid_price;
                            if price_diff.abs() > 0.00005 && !has_diff {
                                error!(
                                    "price diff: {:.6} bn mid price: {:.6} gate mid price: {:?}",
                                    price_diff, bn_last_mid_price, gate_last_mid_price
                                );
                                has_diff = true;
                            } else if has_diff && price_diff.abs() < 0.00002 {
                                error!(
                                    "price diff back to normal: {:.6} bn mid price: {:.6} gate mid price: {:?}",
                                    price_diff, bn_last_mid_price, gate_last_mid_price
                                );
                                has_diff = false;
                            }
                        } else {
                            info!(
                                "failed to parse bbo: {}",
                                String::from_utf8_lossy(data.as_ref())
                            );
                        }
                    }
                    _ => (),
                },
                _ => (),
            },
            CallbackData::ConnectionOpen(token) => match token {
                WS_BBO_1 => {
                    handle.send_message(WS_BBO_1, generate_bbo_subscribe_request())?;
                }
                _ => (),
            },
            CallbackData::ConnectionClose(token, err) => {
                info!("connection close: {:?} {:?}", token, err);
                flush_logs!();
            }
            CallbackData::ConnectionError(token, error) => {
                error!("connection err: {:?}: {:?}", token, error);
                flush_logs!();
            }
        }
        Ok(())
    };
    let mut settings = Settings::default();
    settings.event_loop_timeout = Some(std::time::Duration::from_millis(1));
    let mut websocket = WebSocket::new(settings, callback)?;

    let gate_bbo_url: Url = generate_bbo_url().into();
    info!("sbe bbo url: {}", gate_bbo_url);
    websocket.connect(gate_bbo_url.clone(), WS_BBO_1)?;

    let bn_bbo_url: Url = generate_futures_book_ticker_url(BN_SYMBOL).into();
    info!("bbo url: {}", bn_bbo_url);
    websocket.connect(bn_bbo_url.clone(), WS_BBO_2)?;

    match websocket.run() {
        Ok(_) => (),
        Err(e) => {
            libwebsocket_rs::error!("Websocket run error: {:?}", e);
            libwebsocket_rs::flush_logs!();
        }
    }
    Ok(())
}
