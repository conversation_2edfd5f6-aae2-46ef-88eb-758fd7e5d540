use libc::{
    CPU_SET, CPU_ZERO, SCHED_FIFO, cpu_set_t, pid_t, sched_param, sched_setaffinity,
    sched_setscheduler,
};
use libwebsocket_rs::{engine::arbitrage_runner_futures::run, info, warn};
use std::mem;

unsafe fn bind_to_core(core_id: usize) {
    unsafe {
        let mut set: cpu_set_t = mem::zeroed();
        CPU_ZERO(&mut set);
        CPU_SET(core_id, &mut set);

        let pid: pid_t = 0; // 0 表示当前线程

        let result = sched_setaffinity(pid, mem::size_of::<cpu_set_t>(), &set);

        if result != 0 {
            warn!(
                "Failed to set CPU affinity: {}",
                std::io::Error::last_os_error()
            );
        } else {
            info!("Successfully bound to core {}", core_id);
        }
    }
}

fn main() -> std::result::Result<(), Box<dyn std::error::Error>> {
    unsafe {
        bind_to_core(0);
        let param = sched_param { sched_priority: 99 };
        let pid = std::process::id() as i32;
        let ret = sched_setscheduler(pid, SCHED_FIFO, &param);
        if ret != 0 {
            warn!(
                "Warning: Failed to set SCHED_FIFO: {}",
                std::io::Error::last_os_error()
            );
            warn!("running as normal priority...");
        }
    }

    run()?;

    Ok(())
}
