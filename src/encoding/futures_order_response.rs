use std::{fmt::Display, str::FromStr};

use crate::encoding::futures_order::OrderSide;

#[derive(Debug)]
pub struct OrderError {
    pub id: Option<u64>,
    pub error: String,
}

impl Display for OrderError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "OrderError: id: {:?}, error: {}", self.id, self.error)
    }
}

pub enum FuturesOrderResponse {
    ListenKey(String),
    Error(OrderError),
    Unkown(Option<u64>),
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Clone, Copy)]
pub enum OrderStatus {
    New,
    PartiallyFilled,
    Filled,
    Canceled,
    Rejected,
    Expired,
}

impl FromStr for OrderStatus {
    type Err = ();
    fn from_str(s: &str) -> Result<Self, Self::Err> {
        match s {
            "NEW" => Ok(OrderStatus::New),
            "PARTIALLY_FILLED" => Ok(OrderStatus::PartiallyFilled),
            "FILLED" => Ok(OrderStatus::Filled),
            "CANCELED" => Ok(OrderStatus::Canceled),
            "REJECTED" => Ok(OrderStatus::Rejected),
            "EXPIRED" => Ok(OrderStatus::Expired),
            _ => Err(()),
        }
    }
}

#[derive(Debug)]
pub struct OrderTradeUpdate {
    pub order_id: u64,
    pub order_status: OrderStatus,
    pub order_side: OrderSide,
    pub price: f64,
    pub quantity: f64,
}

#[derive(Debug)]
pub enum UserDataResponse {
    OrderTradeUpdate(OrderTradeUpdate),
}

pub fn parse_futures_order_response(input: &[u8]) -> Option<FuturesOrderResponse> {
    // {"id":"d3df8a61-98ea-4fe0-8f4e-0fcea5d418b0","status":200,"result":{"listenKey":"YNKRAR1MIVOA6Qn90Vv8JgwqXvbAKdpBTBzx1M04b1jfzNlPKa4uRUDx8glXTwWL"},"rateLimits":[{"rateLimitType":"REQUEST_WEIGHT","interval":"MINUTE","intervalNum":1,"limit":2400,"count":8}]}
    // {"id":"1755690542777110","status":429,"error":{"code":-1015,"msg":"Too many new orders; current limit is 300 orders per TEN_SECONDS."},"rateLimits":[{"rateLimitType":"REQUEST_WEIGHT","interval":"MINUTE","intervalNum":1,"limit":-1,"count":-1},{"rateLimitType":"ORDERS","interval":"SECOND","intervalNum":10,"limit":300,"count":2757}]}
    let listen_key_pattern = b"\"listenKey\":\"";
    match memchr::memmem::find(input, listen_key_pattern) {
        Some(idx) => {
            let start = idx + listen_key_pattern.len();
            let end = memchr::memchr(b'"', &input[start..])?;
            let listen_key = &input[start..start + end];
            let listen_key: &str = unsafe { std::mem::transmute(listen_key) };
            Some(FuturesOrderResponse::ListenKey(listen_key.to_string()))
        }
        None => {
            let id_pattern = b"\"id\":\"";
            let start = memchr::memmem::find(input, id_pattern)? + id_pattern.len();
            let end = memchr::memchr(b'"', &input[start..])?;
            let id: &str = unsafe { std::mem::transmute(&input[start..start + end]) };
            let id: Option<u64> = match id.parse() {
                Ok(id) => Some(id),
                Err(_) => None,
            };
            let error_pattern = b"\"error\":";
            if let Some(start) = memchr::memmem::find(input, error_pattern) {
                let start = start + error_pattern.len();
                let end = memchr::memchr(b'}', &input[start..])?;
                let error: &str = unsafe { std::mem::transmute(&input[start..start + end]) };
                Some(FuturesOrderResponse::Error(OrderError {
                    id,
                    error: error.to_string(),
                }))
            } else {
                Some(FuturesOrderResponse::Unkown(id))
            }
        }
    }
}

pub fn parse_user_data_response(input: &[u8]) -> Option<UserDataResponse> {
    /*
    {"e":"ORDER_TRADE_UPDATE","T":1755670485403,"E":1755670485404,"o":{"s":"BTCUSDC","c":"web_jZvk9nkynDRC1GOKdqGv","S":"BUY","o":"MARKET","f":"GTC","q":"0.005","p":"0","ap":"113497.5","sp":"0","x":"TRADE","X":"FILLED","i":23270894736,"l":"0.005","z":"0.005","L":"113497.5","n":"0.00024516","N":"BNB","T":1755670485403,"t":231508024,"b":"0","a":"113.6578","m":false,"R":true,"wt":"CONTRACT_PRICE","ot":"MARKET","ps":"BOTH","cp":false,"rp":"0.63575454","pP":false,"si":0,"ss":0,"V":"EXPIRE_MAKER","pm":"NONE","gtd":0}}
     */
    let order_trade_update_pattern = b"\"e\":\"ORDER_TRADE_UPDATE\"";
    memchr::memmem::find(input, order_trade_update_pattern)?;
    let order_id_pattern = b"\"c\":\"";
    let start = memchr::memmem::find(input, order_id_pattern)? + order_id_pattern.len();
    let end = memchr::memchr(b'"', &input[start..])?;
    let order_id: &str = unsafe { std::mem::transmute(&input[start..start + end]) };
    let order_id: u64 = match order_id.parse() {
        Ok(id) => id,
        Err(_) => {
            return None;
        }
    };
    let order_status_pattern = b"\"X\":\"";
    let start = memchr::memmem::find(input, order_status_pattern)? + order_status_pattern.len();
    let end = memchr::memchr(b'"', &input[start..])?;
    let order_status: &str = unsafe { std::mem::transmute(&input[start..start + end]) };
    let order_side_pattern = b"\"S\":\"";
    let start = memchr::memmem::find(input, order_side_pattern)? + order_side_pattern.len();
    let end = memchr::memchr(b'"', &input[start..])?;
    let order_side: &str = unsafe { std::mem::transmute(&input[start..start + end]) };
    let order_side: OrderSide = order_side.parse().unwrap();
    let price_pattern = b"\"L\":\"";
    let start = memchr::memmem::find(input, price_pattern)? + price_pattern.len();
    let end = memchr::memchr(b'"', &input[start..])?;
    let price: &str = unsafe { std::mem::transmute(&input[start..start + end]) };
    let price: f64 = price.parse().unwrap();
    let quantity_pattern = b"\"l\":\"";
    let start = memchr::memmem::find(input, quantity_pattern)? + quantity_pattern.len();
    let end = memchr::memchr(b'"', &input[start..])?;
    let quantity: &str = unsafe { std::mem::transmute(&input[start..start + end]) };
    let quantity: f64 = quantity.parse().unwrap();
    Some(UserDataResponse::OrderTradeUpdate(OrderTradeUpdate {
        order_id,
        order_status: order_status.parse().unwrap(),
        order_side,
        price,
        quantity,
    }))
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_parse_user_data_response() {
        let input = b"{\"e\":\"ORDER_TRADE_UPDATE\",\"T\":1755670485403,\"E\":1755670485404,\"o\":{\"s\":\"BTCUSDC\",\"c\":\"1234556678\",\"S\":\"BUY\",\"o\":\"MARKET\",\"f\":\"GTC\",\"q\":\"0.005\",\"p\":\"0\",\"ap\":\"113497.5\",\"sp\":\"0\",\"x\":\"TRADE\",\"X\":\"FILLED\",\"i\":23270894736,\"l\":\"0.005\",\"z\":\"0.005\",\"L\":\"113497.5\",\"n\":\"0.00024516\",\"N\":\"BNB\",\"T\":1755670485403,\"t\":231508024,\"b\":\"0\",\"a\":\"113.6578\",\"m\":false,\"R\":true,\"wt\":\"CONTRACT_PRICE\",\"ot\":\"MARKET\",\"ps\":\"BOTH\",\"cp\":false,\"rp\":\"0.63575454\",\"pP\":false,\"si\":0,\"ss\":0,\"V\":\"EXPIRE_MAKER\",\"pm\":\"NONE\",\"gtd\":0}}";
        let response = parse_user_data_response(input).unwrap();
        println!("{:?}", response);
    }
}
