use serde::{Deserialize, Serialize};

#[derive(Debug, Deserialize)]
struct Envelope {
    #[allow(dead_code)]
    time: Option<i64>,
    #[allow(dead_code)]
    time_ms: Option<i64>,
    channel: Option<String>,
    event: Option<String>,
    result: Option<serde_json::Value>,
    // 也可能返回 "error"
    error: Option<serde_json::Value>,
}

#[derive(Debug, Deserialize)]
pub struct GateBookTicker {
    /// 生成时间(毫秒)
    #[serde(default, rename = "t")]
    pub ts: u64,

    /// order book update id
    #[serde(default, rename = "u")]
    pub update_id: serde_json::Value,

    /// 合约名，如 "BTC_USDT"
    #[serde(default, rename = "s")]
    pub symbol: String,

    /// 最优买价
    #[serde(default, rename = "b")]
    pub bid_price: String,

    /// 最优买量
    #[serde(default, rename = "B")]
    pub bid_qty: serde_json::Value,

    /// 最优卖价
    #[serde(default, rename = "a")]
    pub ask_price: String,

    /// 最优卖量
    #[serde(default, rename = "A")]
    pub ask_qty: serde_json::Value,
}
